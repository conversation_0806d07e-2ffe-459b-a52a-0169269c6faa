"use client";

const RecentTransactions = ({ transactionsData }) => {
  // console.log("transactionsData", transactionsData);

  return (
    <>
      <div className="bg-gray-900 text-white min-h-screen p-6 rounded-2xl">
        <div className="">
          <h1 className="text-2xl font-bold mb-6">Recent Transactions</h1>

          <div className="bg-gray-800 rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                      Description
                    </th>
                    <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                      Transaction ID
                    </th>
                    <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                      Type
                    </th>
                    <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                      Amount
                    </th>
                    <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                      Charge
                    </th>
                    <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                      Status
                    </th>
                    <th className="text-left py-4 px-4 text-yellow-400 font-medium text-sm">
                      Method
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {transactionsData.map((transactions) => (
                    <tr
                      key={transactions.tnx}
                      className="border-b border-gray-700 hover:bg-gray-750 transition-colors"
                    >
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-3">
                          <span className={`text-lg text-yellow-400`}>💰</span>
                          <div>
                            <div className="text-white font-medium">
                              {transactions.description}
                            </div>
                            <div className="text-gray-400 text-sm">
                              {transactions.created_at}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4 text-gray-300 font-mono text-sm">
                        {transactions.tnx}
                      </td>
                      <td className="py-4 px-4 text-gray-300">
                        {transactions.type}
                      </td>
                      <td className="py-4 px-4">
                        <span
                          className={
                            transactions.is_plus
                              ? "text-green-400"
                              : "text-red-400"
                          }
                        >
                          {transactions.is_plus ? "+" : "-"}
                          {transactions.amount}
                          <span className="ml-1 text-xs">
                            {transactions.is_plus ? "↑" : "↓"}
                          </span>
                        </span>
                      </td>
                      <td className="py-4 px-4 text-red-400 font-medium">
                        {transactions.charge}
                      </td>
                      <td className="py-4 px-4">
                        <span
                          className={
                            transactions.status === "Failed"
                              ? "text-red-400"
                              : "text-green-400"
                          }
                        >
                          {transactions.status}
                        </span>
                      </td>
                      <td className="py-4 px-4 text-gray-300">
                        {transactions?.method || "Not Set Method"}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default RecentTransactions;
