"use client";
import { useState, useRef } from "react";
import Link from "next/link";
import axios from "axios";

export default function OTPForm() {
  const [otp, setOtp] = useState(Array(6).fill(""));
  const inputsRef = useRef([]);

  // When user types in input
  const handleChange = (element, index) => {
    if (isNaN(element.value)) return;

    const newOtp = [...otp];
    newOtp[index] = element.value;
    setOtp(newOtp);

    // Log each change
    console.log("Current OTP:", newOtp.join(""));

    // Move focus to next input
    if (element.value && index < 5) {
      inputsRef.current[index + 1].focus();
    }
  };

  // Handle backspace to go to previous input
  const handleKeyDown = (e, index) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      inputsRef.current[index - 1].focus();
    }
  };

  // On form submit
  const handleSubmit = (e) => {
    e.preventDefault();
    const otpValue = otp.join("");
    // alert("Entered OTP: " + otp.join(""));
    console.log("Submitted OTP:", otpValue);
    getOtp();
  };

  const getOtp = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/user/two-fa-verify`,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      console.log("OTP Response:", res.data);
      return res.data;
    } catch (err) {
      console.error("Error in otp:", err);
      toast.error("Something went wrong while verifying OTP.");
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="flex flex-col items-center justify-center h-screen">
        <div className="w-full max-w-md bg-white shadow-lg rounded-lg p-8">
          <div className="mb-4">
            <label className="block text-gray-700 text-2xl font-bold mb-5 text-center">
              Enter 2FA Code
            </label>
            <div className="flex justify-center gap-2">
              {otp.map((data, index) => (
                <input
                  key={index}
                  type="text"
                  maxLength="1"
                  value={data}
                  onChange={(e) => handleChange(e.target, index)}
                  onKeyDown={(e) => handleKeyDown(e, index)}
                  ref={(el) => (inputsRef.current[index] = el)}
                  className="w-12 h-12 border rounded text-center text-lg font-bold focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              ))}
            </div>
          </div>

          <div className="flex justify-center">
            <button
              className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full"
              type="submit"
            >
              Login
            </button>
          </div>

          <div className="mt-4 text-center">
            <p className="text-gray-700">
              Not received code?{" "}
              <Link href="" className="text-blue-600">
                Resend
              </Link>
            </p>
          </div>
        </div>
      </div>
    </form>
  );
}
