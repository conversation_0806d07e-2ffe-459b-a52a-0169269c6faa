"use client";

import axios from "axios";
import Cookies from "js-cookie";
import { useEffect, useState } from "react";

const page = () => {
  const [qrCode, setQrCode] = useState("");

  // fetch qr code
  const fetchQrCode = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/qrcode`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      setQrCode(res.data.data);
    } catch (e) {
      console.log(e.response?.data || e);
    }
  };

  useEffect(() => {
    fetchQrCode();
  }, []);

  // download the qr code
  const downloadSVG = () => {
    const blob = new Blob([qrCode], { type: "image/svg+xml" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "qrcode.svg";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="p-4">
      <h1 className="text-xl font-bold mb-4">Your QR Code</h1>

      {qrCode ? (
        <div className="flex flex-col items-center gap-4">
          <div className="border p-2 w-[200px] h-[200px]">
            <div
              className="w-full h-full svg-container"
              dangerouslySetInnerHTML={{ __html: qrCode }}
            />
          </div>

          <button
            onClick={downloadSVG}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Download QR Code
          </button>
        </div>
      ) : (
        <p>Loading QR code...</p>
      )}
    </div>
  );
};

export default page;
