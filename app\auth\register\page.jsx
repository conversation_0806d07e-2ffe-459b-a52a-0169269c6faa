"use client";

import { normalizeFields } from "@/utils";
import axios from "axios";
import Cookies from "js-cookie";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

const page = () => {
  const [loading, setLoading] = useState(false);
  const [countries, setCountries] = useState([]);
  const [fieldConfig, setFieldConfig] = useState({});

  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [username, setUsername] = useState("");
  const [phone, setPhone] = useState("");
  const [country, setCountry] = useState("");
  const [gender, setGender] = useState("");
  const [password, setPassword] = useState("");
  const [passwordConfirm, setPasswordConfirm] = useState("");
  const [terms, setTerms] = useState(false);

  const router = useRouter();

  // After success data remove form the form
  const removeData = () => {
    setFirstName("");
    setLastName("");
    setEmail("");
    setUsername("");
    setPhone("");
    setCountry("");
    setGender("");
    setPassword("");
    setPasswordConfirm("");
    setTerms(false);
  };

  // country data fetch
  const fetchCountry = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/get-countries`
      );
      setCountries(res.data.data || []);
      const findCountry = res.data.data.find(
        (country) => country.selected === true
      );

      setCountry(`${findCountry.dial_code}:${findCountry.name}`);
    } catch (e) {
      console.error("country error:", e.response.data);
    }
  };

  // fetch field and required true or false
  const fetchField = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/get-register-fields/user`
      );
      const config = normalizeFields(res.data.data);
      setFieldConfig(config);
    } catch (e) {
      console.error("field error:", e.response.data);
    }
  };

  // handle register part
  const handleRegister = async (e) => {
    e.preventDefault();
    if (!validateFields()) return;

    setLoading(true);
    try {
      const requestBody = {
        first_name: firstName,
        last_name: lastName,
        email: email,
        password: password,
        password_confirmation: passwordConfirm,
        i_agree: terms,
        // ...(fieldConfig.username?.validation ? { username } : {}),
        // ...(fieldConfig.phone?.validation ? { phone } : {}),
        // ...(fieldConfig.country?.validation ? { country } : {}),
        // ...(fieldConfig.gender?.validation ? { gender } : {}),
      };

      if (fieldConfig.username?.validation) {
        requestBody.username = username;
      }
      if (fieldConfig.phone?.validation) {
        requestBody.phone = phone;
      }
      if (fieldConfig.country?.validation) {
        requestBody.country = country;
      }
      if (fieldConfig.gender?.validation) {
        requestBody.gender = gender;
      }

      // console.log("Request body:", requestBody);

      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/user/register`,
        requestBody,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      Cookies.set("token", res.data.data.token);
      removeData();
      toast.success("Register successful!");
      await getSettings();
    } catch (e) {
      if (e.response?.status === 422) {
        console.log("register error:", e.response.data.message);
        toast.error(e.response.data.message);
      } else if (e.response?.status === 401) {
        Cookies.remove("token");
        router.push("/auth/login");
      } else {
        console.log("register error:", e.response.data.message);
      }
    } finally {
      setLoading(false);
    }
  };

  // get settings part
  const getSettings = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/get-settings?key=email_verification`
      );
      if (res.data.data === "0") {
        router.push("/dashboard");
      } else if (res.data.data === "1") {
        console.log("email verification is enabled");

        await sendVerifyEmail();
      }
    } catch (e) {
      console.error("settings error:", e.response.data);
    }
  };

  // email verification part
  const sendVerifyEmail = async () => {
    try {
      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/user/send-verify-email`,
        {},
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      console.log("verify email success:", res.data);

      router.push("register/email-verification");
    } catch (e) {
      if (e.response?.status === 401) {
        Cookies.remove("token");
        router.push("/auth/login");
      }
      console.error("verify email error:", e.response?.data || e);
    }
  };

  // validation error show here
  const validateFields = () => {
    if (firstName === "") {
      toast.error("First name is required.");
      return false;
    }

    if (lastName === "") {
      toast.error("Last name is required.");
      return false;
    }

    if (email === "") {
      toast.error("Email is required.");
      return false;
    }

    if (fieldConfig.username?.validation && username === "") {
      toast.error("Username is required.");
      return false;
    }

    if (fieldConfig.phone?.validation && phone === "") {
      toast.error("Phone is required.");
      return false;
    }

    if (fieldConfig.country?.validation && country === "") {
      toast.error("Country is required.");
      return false;
    }

    if (fieldConfig.gender?.validation && gender === "") {
      toast.error("Gender is required.");
      return false;
    }

    if (password === "") {
      toast.error("Password is required.");
      return false;
    }

    if (passwordConfirm === "") {
      toast.error("Confirm password is required.");
      return false;
    }

    if (password !== passwordConfirm) {
      toast.error("Passwords do not match.");
      return false;
    }

    return true;
  };

  useEffect(() => {
    fetchCountry();
    fetchField();
  }, []);

  return (
    <div>
      <div className="flex flex-col items-center justify-center h-screen">
        <div className="w-full max-w-md bg-white shadow-lg rounded-lg p-8">
          <h1 className="text-3xl font-bold mb-5">Register</h1>
          <form onSubmit={handleRegister}>
            <div className="mb-4">
              <label
                className="block text-gray-700 text-sm font-bold mb-2"
                htmlFor="first_name"
              >
                First Name <span className="text-red-500">*</span>
              </label>
              <input
                className="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="first_name"
                type="text"
                placeholder="First Name"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
              />
            </div>
            <div className="mb-4">
              <label
                className="block text-gray-700 text-sm font-bold mb-2"
                htmlFor="last_name"
              >
                Last Name <span className="text-red-500">*</span>
              </label>
              <input
                className="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="last_name"
                type="text"
                placeholder="Last Name"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
              />
            </div>
            <div className="mb-4">
              <label
                className="block text-gray-700 text-sm font-bold mb-2"
                htmlFor="email"
              >
                Email <span className="text-red-500">*</span>
              </label>
              <input
                className="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="email"
                type="text"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            {fieldConfig.username?.show && (
              <div className="mb-4">
                <label
                  className="block text-gray-700 text-sm font-bold mb-2"
                  htmlFor="username"
                >
                  Username{" "}
                  {fieldConfig.username?.validation && (
                    <span className="text-red-500">*</span>
                  )}
                </label>
                <input
                  className="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  id="username"
                  type="text"
                  placeholder="Username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                />
              </div>
            )}
            {fieldConfig.phone?.show && (
              <div className="mb-4">
                <label
                  className="block text-gray-700 text-sm font-bold mb-2"
                  htmlFor="phone"
                >
                  Phone{" "}
                  {fieldConfig.phone?.validation && (
                    <span className="text-red-500">*</span>
                  )}
                </label>
                <input
                  className="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  id="phone"
                  type="text"
                  placeholder="Phone"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                />
              </div>
            )}
            {fieldConfig.country?.show && (
              <div className="mb-4">
                <label
                  className="block text-gray-700 text-sm font-bold mb-2"
                  htmlFor="country"
                >
                  Country{" "}
                  {fieldConfig.country?.validation && (
                    <span className="text-red-500">*</span>
                  )}
                </label>
                <select
                  className="border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  id="country"
                  value={country}
                  onChange={(e) => setCountry(e.target.value)}
                >
                  {/* <option value="">Select Country</option> */}
                  {countries.map((country) => (
                    <option
                      key={country.name}
                      value={`${country.dial_code}:${country.name}`}
                    >
                      {country.name}
                    </option>
                  ))}
                </select>
              </div>
            )}
            {fieldConfig.gender?.show && (
              <div className="mb-4">
                <label
                  className="block text-gray-700 text-sm font-bold mb-2"
                  htmlFor="gender"
                >
                  Gender{" "}
                  {fieldConfig.gender?.validation && (
                    <span className="text-red-500">*</span>
                  )}
                </label>
                <select
                  className="border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  id="gender"
                  value={gender}
                  onChange={(e) => setGender(e.target.value)}
                >
                  <option value="">Select Gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                </select>
              </div>
            )}
            <div className="mb-4">
              <label
                className="block text-gray-700 text-sm font-bold mb-2"
                htmlFor="password"
              >
                Password <span className="text-red-500">*</span>
              </label>
              <input
                className="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="password"
                type="text"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
            <div className="mb-4">
              <label
                className="block text-gray-700 text-sm font-bold mb-2"
                htmlFor="password_confirmation"
              >
                Confirm Password <span className="text-red-500">*</span>
              </label>
              <input
                className="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="password_confirmation"
                type="text"
                placeholder="Confirm Password"
                value={passwordConfirm}
                onChange={(e) => setPasswordConfirm(e.target.value)}
              />
            </div>
            <div className="mb-4">
              <label className="inline-flex items-center">
                <input
                  type="checkbox"
                  className="form-checkbox h-5 w-5 text-blue-600"
                  checked={terms}
                  onChange={(e) => setTerms(e.target.checked)}
                />
                <span className="ml-2 text-gray-700">
                  Agree terms and conditions
                </span>
              </label>
            </div>
            <div className="flex justify-center">
              <button
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full"
                type="submit"
              >
                {loading ? "Submitting..." : "Register"}
              </button>
            </div>
            <div className="mt-4 text-center">
              <p className="text-gray-700">
                Already have an account?{" "}
                <Link href="/auth/login" className="text-blue-600">
                  Login
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default page;
