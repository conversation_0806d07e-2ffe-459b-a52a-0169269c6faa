"use client";

import { useSettings } from "@/context/SettingsContext";
import {
  calculateCharge,
  calculateMinMaxAmount,
  dynamicDecimals,
  getSettingValue,
} from "@/utils";
import axios from "axios";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const AddMoney = () => {
  const [step, setStep] = useState(1);
  const [wallets, setWallets] = useState([]);
  const [walletsLoading, setWalletsLoading] = useState(false);

  const [selectedWalletCode, setSelectedWalletCode] = useState("");
  const [gateway, setGateway] = useState([]);
  const [gatewaysLoading, setGatewaysLoading] = useState(false);

  const [selectedGateway, setSelectedGateway] = useState("");
  const { settings } = useSettings();
  const [gatewayMethod, setGatewayMethod] = useState({});
  const siteCurrency = getSettingValue(settings, "site_currency");

  // charge state
  const [mainCharge, setMainCharge] = useState("");
  const [displayChargeType, setDisplayChargeType] = useState("");
  const [minAmount, setMinAmount] = useState("");
  const [maxAmount, setMaxAmount] = useState("");

  // charge calculation states
  const [calculatedCharge, setCalculatedCharge] = useState(0);
  const [totalAmount, setTotalAmount] = useState(0);

  // amount input state
  const [amount, setAmount] = useState("");

  // submit payment data
  const [submitPaymentData, setSubmitPaymentData] = useState([]);

  // manual gateway fields state
  const [manualFields, setManualFields] = useState({});
  const [errors, setErrors] = useState({});

  // next js router
  const router = useRouter();

  // find selected gateway and wallet data
  const selectedGatewayData = gateway.find(
    (item) => item.id === Number(selectedGateway)
  );
  const selectedWalletData = wallets.find(
    (item) => item.code === String(selectedWalletCode)
  );

  // get wallets data
  const walletsData = async () => {
    try {
      setWalletsLoading(true);
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/wallets`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
        }
      );
      setWallets(res.data.data.wallets);
    } catch (e) {
      console.log(e.response.data);
    } finally {
      setWalletsLoading(false);
    }
  };

  // get gateway data dynamically
  const gatewayData = async () => {
    try {
      setGatewaysLoading(true);
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/add-money`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
          params: {
            currency: selectedWalletCode,
          },
        }
      );
      setGateway(res.data.data);
    } catch (e) {
      console.log(e.response.data);
    } finally {
      setGatewaysLoading(false);
    }
  };

  // Step navigation
  const nextStep = () => setStep((prev) => Math.min(prev + 1, 3));
  const prevStep = () => setStep((prev) => Math.max(prev - 1, 1));
  const resetSteps = () => setStep(1);

  // charge amount calculate
  const reviewCalculate = () => {
    const baseAmount = parseFloat(amount);
    let chargeValue = 0;

    if (gatewayMethod?.charge_type?.toLowerCase() === "percentage") {
      chargeValue = (baseAmount * parseFloat(gatewayMethod.charge)) / 100;
    } else if (gatewayMethod?.charge_type?.toLowerCase() === "fixed") {
      chargeValue = parseFloat(gatewayMethod.charge);
    }

    const total = baseAmount + chargeValue;

    setCalculatedCharge(chargeValue);
    setTotalAmount(total);
  };

  useEffect(() => {
    walletsData();
  }, []);

  // fetch gateway when wallet is selected
  useEffect(() => {
    if (selectedWalletCode) {
      gatewayData(selectedWalletCode);
    } else {
      setGateway([]);
    }
  }, [selectedWalletCode]);

  // calculate charge and min max amount whenever gatewayMethod changes
  useEffect(() => {
    if (selectedGateway) {
      const chargeResult = calculateCharge(gatewayMethod, siteCurrency);
      setMainCharge(chargeResult.mainCharge);
      setDisplayChargeType(chargeResult.displayChargeType);

      const minMaxResult = calculateMinMaxAmount(gatewayMethod, siteCurrency);
      setMinAmount(minMaxResult.minAmount);
      setMaxAmount(minMaxResult.maxAmount);

      // Reset manual fields when gateway changes
      if (gatewayMethod?.type === "manual") {
        const initialValues = {};
        gatewayMethod.field_options?.forEach((field) => {
          initialValues[field.name] = "";
        });
        setManualFields(initialValues);
      }
    }
  }, [selectedGateway, gatewayMethod]);

  useEffect(() => {
    if (amount && gatewayMethod?.charge_type) {
      reviewCalculate();
    }
  }, [amount, gatewayMethod]);

  // Validation before going to next step
  const handleNextStep = () => {
    let newErrors = {};

    if (!selectedWalletCode) newErrors.wallet = "Wallet is required";
    if (!selectedGateway) newErrors.gateway = "Gateway is required";
    if (!amount) newErrors.amount = "Amount is required";

    if (
      amount &&
      (parseFloat(amount) < parseFloat(minAmount) ||
        parseFloat(amount) > parseFloat(maxAmount))
    ) {
      newErrors.amount = `Amount must be between ${minAmount} and ${maxAmount}`;
    }

    if (gatewayMethod.type === "manual") {
      gatewayMethod.field_options?.forEach((field) => {
        if (field.validation === "required" && !manualFields[field.name]) {
          newErrors[field.name] = `${field.name} is required`;
        }
      });
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      nextStep();
    }
  };

  // Handle manual field change
  const handleManualFieldChange = (fieldName, value) => {
    setManualFields((prev) => ({ ...prev, [fieldName]: value }));
  };

  const handleManualPayment = async () => {
    try {
      const formData = new FormData();

      // basic fields
      formData.append("payment_gateway", selectedGateway);
      formData.append(
        "user_wallet",
        selectedWalletData?.is_default ? "default" : selectedWalletData?.id
      );
      formData.append("amount", amount);

      // append manual_data fields
      Object.entries(manualFields).forEach(([key, value]) => {
        if (value instanceof File) {
          formData.append(`manual_data[${key}]`, value);
        } else {
          formData.append(`manual_data[${key}]`, value);
        }
      });

      console.log("FormData entries:");
      for (let pair of formData.entries()) {
        console.log(pair[0], pair[1]);
      }

      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/add-money`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "multipart/form-data",
          },
        }
      );

      console.log("payment success:", res.data);
      setSubmitPaymentData(res.data.data);
      setStep(3);
    } catch (e) {
      console.log("payment error:", e.response?.data || e.message);
    }
  };

  const handleAutoPayment = async () => {
    console.log("Auto payment");
    try {
      const requestBody = {
        payment_gateway: selectedGateway,
        user_wallet: selectedWalletData?.is_default
          ? "default"
          : selectedWalletData?.id,
        amount: amount,
      };
      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/add-money`,
        requestBody,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
        }
      );
      console.log("payment success:", res.data);
      if (res.data?.data?.redirect_url) {
        window.location.href = res.data.data.redirect_url;
        return;
      }

      router.push("/dashboard");
    } catch (e) {
      console.log("payment error:", e.response?.data);
    }
  };

  return (
    <div>
      {/* Step Indicators */}
      <div className="max-w-lg mx-auto mb-4">
        <div className="flex justify-between">
          {[1, 2, 3].map((s) => (
            <div key={s} className={`step-${s}`}>
              <div className="flex flex-col items-center">
                <div
                  className={`step-number w-8 h-8 flex justify-center items-center rounded-full 
                    ${step === s ? "bg-blue-600 text-white" : "bg-gray-200"}`}
                >
                  {s}
                </div>
                <div className="step-name">
                  {s === 1 ? "Amount" : s === 2 ? "Review" : "Success"}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="max-w-lg mx-auto bg-white shadow-lg rounded-lg p-8">
        {step === 1 && (
          <div className="step-1-content">
            <div className="grid grid-cols-12 gap-5">
              {/* Wallet */}
              <div className="col-span-12">
                <label className="block text-gray-700 text-sm font-bold mb-2">
                  Wallet
                </label>
                <select
                  value={selectedWalletCode}
                  onChange={(e) => {
                    setSelectedWalletCode(e.target.value);
                    setSelectedGateway("");
                    setGatewayMethod({});
                    setMainCharge("");
                    setDisplayChargeType("");
                    setMinAmount("");
                    setMaxAmount("");
                    setManualFields({});
                  }}
                  className="border rounded w-full py-2 px-3 text-gray-700"
                  disabled={walletsLoading}
                >
                  <option value="">
                    {walletsLoading ? "Loading wallets..." : "Select Wallet"}
                  </option>
                  {wallets.map((wallet) => (
                    <option key={wallet.id} value={wallet.code}>
                      {wallet.name}
                    </option>
                  ))}
                </select>
                {errors.wallet && (
                  <p className="text-red-500 text-sm">{errors.wallet}</p>
                )}
              </div>

              {/* Gateway */}
              <div className="col-span-12">
                <label className="block text-gray-700 text-sm font-bold mb-2">
                  Gateway
                </label>
                <select
                  value={selectedGateway}
                  onChange={(e) => {
                    const selectedId = e.target.value;
                    setSelectedGateway(selectedId);

                    const selected = gateway.find(
                      (g) => g.id === parseInt(selectedId)
                    );
                    if (selected) {
                      setGatewayMethod(selected);
                    }
                  }}
                  className="border rounded w-full py-2 px-3 text-gray-700"
                  disabled={gatewaysLoading}
                >
                  <option value="">
                    {gatewaysLoading ? "Loading gateways..." : "Select Gateway"}
                  </option>
                  {gateway.map((gateway) => (
                    <option key={gateway.id} value={gateway.id}>
                      {gateway.formatted_name}
                    </option>
                  ))}
                </select>
                {errors.gateway && (
                  <p className="text-red-500 text-sm">{errors.gateway}</p>
                )}
                {mainCharge && displayChargeType && (
                  <p className="text-sm text-red-500">
                    Charge: {mainCharge} {displayChargeType}
                  </p>
                )}
              </div>

              {/* Amount */}
              <div className="col-span-12">
                <label className="block text-gray-700 text-sm font-bold mb-2">
                  Amount
                </label>
                <input
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="border rounded w-full py-2 px-3 text-gray-700"
                  type="number"
                  placeholder="Amount"
                />
                {errors.amount && (
                  <p className="text-red-500 text-sm">{errors.amount}</p>
                )}
                {minAmount && maxAmount && (
                  <p className="text-red-500 text-sm">
                    Minimum: {minAmount} {gatewayMethod.currency} and Maximum:{" "}
                    {maxAmount} {gatewayMethod.currency}
                  </p>
                )}
              </div>

              {/* Manual Gateway Fields */}
              {selectedGateway && gatewayMethod.type === "manual" && (
                <div className="col-span-12">
                  {gatewayMethod.field_options?.map((field, index) => (
                    <div key={index} className="mb-4">
                      <label className="block text-gray-700 text-sm font-bold mb-2">
                        {field.name}{" "}
                        {field.validation === "required" && (
                          <span className="text-red-500">*</span>
                        )}
                      </label>

                      {field.type === "textarea" ? (
                        <textarea
                          value={manualFields[field.name] || ""}
                          onChange={(e) =>
                            handleManualFieldChange(field.name, e.target.value)
                          }
                          className="border rounded w-full py-2 px-3 text-gray-700"
                          placeholder={field.name}
                        />
                      ) : field.type === "file" ? (
                        <input
                          type="file"
                          onChange={(e) =>
                            handleManualFieldChange(
                              field.name,
                              e.target.files[0]
                            )
                          }
                          className="block w-full text-sm text-gray-700 border border-gray-300 rounded"
                        />
                      ) : (
                        <input
                          value={manualFields[field.name] || ""}
                          onChange={(e) =>
                            handleManualFieldChange(field.name, e.target.value)
                          }
                          className="border rounded w-full py-2 px-3 text-gray-700"
                          type={field.type}
                          placeholder={field.name}
                        />
                      )}
                      {errors[field.name] && (
                        <p className="text-red-500 text-sm">
                          {errors[field.name]}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {/* Next Button */}
              <div className="col-span-12 flex justify-end">
                <button
                  onClick={handleNextStep}
                  className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}

        {step === 2 && (
          <div className="step-2-content">
            <p className="mb-4 text-xl font-medium border-b border-b-gray-500 pb-2">
              Review Details
            </p>
            <div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-bold mb-2">Amount</h3>
                <p>
                  {parseFloat(amount).toFixed(
                    dynamicDecimals({
                      currencyCode: selectedGatewayData.currency,
                      siteCurrencyCode: getSettingValue(
                        settings,
                        "site_currency"
                      ),
                      siteCurrencyDecimals: getSettingValue(
                        settings,
                        "site_currency_decimals"
                      ),
                      isCrypto:
                        selectedGatewayData.currency_type === "crypto"
                          ? true
                          : false,
                    })
                  )}{" "}
                  {gatewayMethod.currency}
                </p>
              </div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-bold mb-2">Wallet Name</h3>
                <p>{selectedWalletData.name}</p>
              </div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-bold mb-2">Payment Method</h3>
                <p>{selectedGatewayData.name}</p>
              </div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-bold mb-2">Charge</h3>
                <p>
                  {calculatedCharge.toFixed(
                    dynamicDecimals({
                      currencyCode: selectedGatewayData.currency,
                      siteCurrencyCode: getSettingValue(
                        settings,
                        "site_currency"
                      ),
                      siteCurrencyDecimals: getSettingValue(
                        settings,
                        "site_currency_decimals"
                      ),
                      isCrypto:
                        selectedGatewayData.currency_type === "crypto"
                          ? true
                          : false,
                    })
                  )}{" "}
                  {gatewayMethod.currency}
                </p>
              </div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-bold mb-2">Total</h3>
                <p>
                  {totalAmount.toFixed(
                    dynamicDecimals({
                      currencyCode: selectedGatewayData.currency,
                      siteCurrencyCode: getSettingValue(
                        settings,
                        "site_currency"
                      ),
                      siteCurrencyDecimals: getSettingValue(
                        settings,
                        "site_currency_decimals"
                      ),
                      isCrypto:
                        selectedGatewayData.currency_type === "crypto"
                          ? true
                          : false,
                    })
                  )}{" "}
                  {gatewayMethod.currency}
                </p>
              </div>

              {/* Manual Fields Review */}
              {gatewayMethod.type === "manual" &&
                Object.entries(manualFields).map(([key, value]) => (
                  <div
                    key={key}
                    className="flex justify-between items-center mb-3"
                  >
                    <h3 className="font-bold mb-2">{key}</h3>
                    <p>{value?.name || value?.toString()}</p>
                  </div>
                ))}
            </div>
            <div className="flex justify-between">
              <button
                onClick={prevStep}
                className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded"
              >
                Back
              </button>
              <button
                onClick={
                  selectedGatewayData?.type === "auto"
                    ? handleAutoPayment
                    : selectedGatewayData?.type === "manual"
                    ? handleManualPayment
                    : undefined
                }
                className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
              >
                Pay
              </button>
            </div>
          </div>
        )}

        {step === 3 && (
          <div className="step-3-content text-center">
            <div className="mb-4 border-b pb-4">
              <p className="mb-4 text-xl font-semibold">
                Your Deposit Process is Pending!
              </p>
              <p>
                {parseFloat(amount).toFixed(
                  dynamicDecimals({
                    currencyCode: selectedGatewayData.currency,
                    siteCurrencyCode: getSettingValue(
                      settings,
                      "site_currency"
                    ),
                    siteCurrencyDecimals: getSettingValue(
                      settings,
                      "site_currency_decimals"
                    ),
                    isCrypto:
                      selectedGatewayData.currency_type === "crypto"
                        ? true
                        : false,
                  })
                )}{" "}
                {gatewayMethod.currency} Deposit Pending
              </p>
            </div>
            <div className="mb-4">
              <p className="font-bold">Amount</p>
              <p>
                {parseFloat(amount).toFixed(
                  dynamicDecimals({
                    currencyCode: selectedGatewayData.currency,
                    siteCurrencyCode: getSettingValue(
                      settings,
                      "site_currency"
                    ),
                    siteCurrencyDecimals: getSettingValue(
                      settings,
                      "site_currency_decimals"
                    ),
                    isCrypto:
                      selectedGatewayData.currency_type === "crypto"
                        ? true
                        : false,
                  })
                )}{" "}
                {gatewayMethod.currency}
              </p>
            </div>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="border p-3 rounded-lg">
                <p className="font-bold">Transaction ID</p>
                <p>{submitPaymentData?.transaction?.tnx}</p>
              </div>
              <div className="border p-3 rounded-lg">
                <p className="font-bold">Charge</p>
                <p>
                  {calculatedCharge.toFixed(
                    dynamicDecimals({
                      currencyCode: selectedGatewayData.currency,
                      siteCurrencyCode: getSettingValue(
                        settings,
                        "site_currency"
                      ),
                      siteCurrencyDecimals: getSettingValue(
                        settings,
                        "site_currency_decimals"
                      ),
                      isCrypto:
                        selectedGatewayData.currency_type === "crypto"
                          ? true
                          : false,
                    })
                  )}{" "}
                  {gatewayMethod.currency}
                </p>
              </div>
              <div className="border p-3 rounded-lg">
                <p className="font-bold">Type</p>
                <p>{submitPaymentData?.transaction?.type}</p>
              </div>
              <div className="border p-3 rounded-lg">
                <p className="font-bold">Final Amount</p>
                <p>
                  {totalAmount.toFixed(
                    dynamicDecimals({
                      currencyCode: selectedGatewayData.currency,
                      siteCurrencyCode: getSettingValue(
                        settings,
                        "site_currency"
                      ),
                      siteCurrencyDecimals: getSettingValue(
                        settings,
                        "site_currency_decimals"
                      ),
                      isCrypto:
                        selectedGatewayData.currency_type === "crypto"
                          ? true
                          : false,
                    })
                  )}{" "}
                  {gatewayMethod.currency}
                </p>
              </div>
            </div>
            <div className="flex justify-end">
              <button
                onClick={resetSteps}
                className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
              >
                Deposit Again
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AddMoney;
