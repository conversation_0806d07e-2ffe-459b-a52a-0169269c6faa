"use client";
import axios from "axios";
import Cookies from "js-cookie";
import Link from "next/link";
import { useEffect, useState } from "react";

const InvoiceHistory = () => {
  const [invoiceHistory, setInvoiceHistory] = useState([]);
  console.log("invoiceHistory:", invoiceHistory);
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState();
  const [lastPage, setLastPage] = useState();
  const [loading, setLoading] = useState(false);

  const getInvoiceHistory = async () => {
    setLoading(true);
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/invoices`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
          params: {
            page: currentPage,
          },
        }
      );
      console.log(res.data.data);
      setInvoiceHistory(res.data.data.invoices);
      const perPage = res.data.data.pagination.per_page;
      setPerPage(perPage);
      const lastPage = res.data.data.pagination.last_page;
      setLastPage(lastPage);
    } catch (e) {
      console.log(e.response.data);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getInvoiceHistory();
  }, [currentPage]);

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <div className="max-w-full mx-auto bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200">
        {/* Table Container */}
        <div className="overflow-x-auto">
          <table className="w-full">
            {/* Table Header */}
            <thead className="bg-gray-100 border-b border-gray-200">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                  SL No
                </th>
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                  Invoice To
                </th>
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                  Email Address
                </th>
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                  Amount
                </th>
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                  Charge
                </th>
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                  Issue Date
                </th>
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                  Payment Status
                </th>
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                  Status
                </th>
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                  Action
                </th>
              </tr>
            </thead>
            {loading ? (
              <tbody>
                <tr>
                  <td colSpan={9} className="py-4 px-6 text-center">
                    Loading...
                  </td>
                </tr>
              </tbody>
            ) : (
              <tbody>
                {invoiceHistory.map((invoice, index) => (
                  <tr
                    key={invoice.id}
                    className="hover:bg-gray-50 transition-colors duration-200"
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                      {index + 1}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {invoice.to}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                      {invoice.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                      {invoice.total_amount}
                      {invoice.currency}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {invoice.charge} {invoice.currency}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                      {new Date(invoice.issue_date).toLocaleDateString(
                        "en-US",
                        {
                          day: "2-digit",
                          month: "short",
                          year: "numeric",
                        }
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                          invoice.is_paid
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {invoice.is_paid ? "Paid" : "Unpaid"}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                          invoice.is_published
                            ? "bg-blue-100 text-blue-800"
                            : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {invoice.is_published ? "Published" : "Draft"}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <div className="flex items-center gap-2">
                        <Link
                          href={`/dashboard/invoice/${invoice.id}/edit`}
                          className="inline-flex items-center px-3 py-1 rounded-md text-xs font-medium bg-green-100 text-green-700 hover:bg-green-200 transition-colors duration-200"
                        >
                          <span className="mr-1">✏️</span>
                          Edit
                        </Link>
                        <Link
                          href={`/dashboard/invoice/${invoice.id}/view`}
                          className="inline-flex items-center px-3 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-700 hover:bg-blue-200 transition-colors duration-200"
                        >
                          <span className="mr-1">👁️</span>
                          View
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            )}
          </table>
        </div>
      </div>
      {invoiceHistory.length > 0 && (
        <div className="flex items-center justify-center mt-6">
          <nav className="flex items-center space-x-1">
            {/* Prev button */}
            <button
              onClick={async () => {
                setCurrentPage((prev) => Math.max(prev - 1, 1));
                await getInvoiceHistory();
              }}
              disabled={currentPage === 1}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded-lg transition-colors duration-200 disabled:opacity-50"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 19l-7-7 7-7"
                ></path>
              </svg>
            </button>

            {/* Page numbers */}
            {[...Array(lastPage)].map((_, i) => {
              const pageNum = i + 1;
              return (
                <button
                  key={pageNum}
                  onClick={() => setCurrentPage(pageNum)}
                  className={`w-8 h-8 rounded-lg font-medium text-sm transition-colors duration-200 ${
                    currentPage === pageNum
                      ? "bg-blue-600 text-white"
                      : "text-gray-500 hover:text-gray-700 hover:bg-gray-200"
                  }`}
                >
                  {pageNum}
                </button>
              );
            })}

            {/* Next button */}
            <button
              onClick={async () => {
                setCurrentPage((next) => Math.min(next + 1, lastPage));
                await getInvoiceHistory();
              }}
              disabled={currentPage === lastPage}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded-lg transition-colors duration-200 disabled:opacity-50"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 5l7 7-7 7"
                ></path>
              </svg>
            </button>
          </nav>
        </div>
      )}
    </div>
  );
};

export default InvoiceHistory;
