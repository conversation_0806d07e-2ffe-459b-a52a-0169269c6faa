"use client";

import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";
import axios from "axios";
import Cookies from "js-cookie";

// Dynamically import ApexCharts (SSR safe)
const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });

const ActivityLineChart = ({ wallets }) => {
  const [chartData, setChartData] = useState(null);
  const [loading, setLoading] = useState(true);

  // filter state
  const [currency, setCurrency] = useState("");
  const [month, setMonth] = useState(new Date().getMonth() + 1);
  const [year, setYear] = useState(new Date().getFullYear());

  // console.log("receive data:", wallets);
  // console.log(month);
  // console.log(year);
  // console.log(currency);

  const fetchApexChart = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/activity-chart`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          params: {
            wallet_id: currency,
            year: year,
            month: month,
          },
        }
      );
      // console.log("Activity chart data Data:", res.data);
      setChartData(res.data.data);
    } catch (err) {
      console.error("API Error:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (wallets?.length > 0 && !currency) {
      setCurrency(wallets[0].is_default == true ? "default" : '');
    }

    fetchApexChart();
  }, [wallets, currency, month, year]);

  if (loading) return <p>Loading chart...</p>;
  if (!chartData) return <p>No chart data available</p>;

  const transformDataToArray = (dataObj) => {
    if (!dataObj) return [];
    return Object.values(dataObj);
  };

  const dateLabels = chartData.deposit
    ? Object.keys(chartData.deposit).map((key) => {
        const dayMatch = key.match(/^(\d+)/);
        return dayMatch ? `${dayMatch[1]}` : key;
      })
    : [];

  const options = {
    chart: {
      height: 380,
      type: "line",
      foreColor: "#fff",
      zoom: { enabled: false },
      toolbar: { show: false },
    },
    grid: {
      show: true,
      borderColor: "rgba(211, 211, 211, 0.5)",
    },
    colors: ["#2D60FF", "#FEAA09", "#9209FE"],
    dataLabels: { enabled: false },
    stroke: { curve: "smooth", width: [4, 4, 4] },
    xaxis: {
      categories: dateLabels,
      labels: {
        style: {
          colors: "#999999",
          fontSize: "12px",
          fontFamily: "Inter",
          fontWeight: 500,
        },
      },
      axisBorder: { show: false },
      axisTicks: { show: false },
    },
    yaxis: {
      min: 0,
      labels: {
        style: {
          colors: "#999999",
          fontSize: "12px",
          fontFamily: "Inter",
          fontWeight: 500,
        },
        formatter: (y) => {
          if (y >= 1000) return `${(y / 1000).toFixed(1)}K`;
          return `${y?.toFixed(0) || 0}`;
        },
        offsetX: -10,
      },
      axisBorder: { show: false },
      axisTicks: { show: false },
    },
    markers: {
      size: [6, 0, 0],
      colors: ["#fff"],
      strokeColors: ["#2D60FF", "#FEAA09", "#9209FE"],
      strokeWidth: 3,
    },
    legend: {
      position: "top",
      horizontalAlign: "left",
      markers: { width: 12, height: 12, radius: 12 },
      itemMargin: { horizontal: 15, vertical: 0 },
      labels: { colors: "#fff" },
    },
    tooltip: {
      enabled: true,
      shared: true,
      intersect: false,
      theme: "dark",
      x: { show: true },
      y: {
        formatter: function (val) {
          return val?.toFixed(0) || 0;
        },
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          legend: {
            position: "bottom",
            horizontalAlign: "center",
          },
        },
      },
    ],
  };

  const series = [
    { name: "Deposit", data: transformDataToArray(chartData?.deposit) },
    { name: "Transfer", data: transformDataToArray(chartData?.transfer) },
    { name: "Withdraw", data: transformDataToArray(chartData?.withdraw) },
  ];

  return (
    <div className="w-full relative">
      {/* Filter dropdowns */}
      <div className="flex flex-wrap justify-end gap-2 absolute right-0 top-[-20px] z-10">
        <select
          value={currency}
          onChange={(e) => {
            setCurrency(e.target.value);
            console.log("Selected Wallet ID:", e.target.value);
          }}
          className="p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          {wallets.map((wallet) => (
            <option
              key={wallet.is_default === true ? "default" : wallet.id}
              value={wallet.is_default === true ? "default" : wallet.id}
            >
              {wallet.name}
            </option>
          ))}
        </select>

        <select
          value={month}
          onChange={(e) => setMonth(e.target.value)}
          className="p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">Select a month</option>
          <option value="1">January</option>
          <option value="2">February</option>
          <option value="3">March</option>
          <option value="4">April</option>
          <option value="5">May</option>
          <option value="6">June</option>
          <option value="7">July</option>
          <option value="8">August</option>
          <option value="9">September</option>
          <option value="10">October</option>
          <option value="11">November</option>
          <option value="12">December</option>
        </select>

        <select
          value={year}
          onChange={(e) => setYear(e.target.value)}
          className="p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">Select a year</option>
          <option value="2020">2020</option>
          <option value="2021">2021</option>
          <option value="2022">2022</option>
          <option value="2023">2023</option>
          <option value="2024">2024</option>
          <option value="2025">2025</option>
        </select>
      </div>

      {/* Chart */}
      <Chart options={options} series={series} type="line" height={380} />
    </div>
  );
};

export default ActivityLineChart;
