"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

const DashboardSidebar = () => {
  const currentPath = usePathname();

  return (
    <aside className="w-64 bg-gray-900 text-white p-5">
      <h2 className="text-xl font-bold mb-6">My Dashboard</h2>
      <nav className="flex flex-col gap-2">
        <Link
          href="/dashboard"
          className={
            currentPath === "/dashboard"
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Home
        </Link>

        <Link
          href="/dashboard/my-wallet"
          className={
            currentPath === "/dashboard/my-wallet"
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          My Wallet
        </Link>

        <Link
          href="/dashboard/qr-code"
          className={
            currentPath === "/dashboard/qr-code"
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          QR Code
        </Link>

        <Link
          href="/dashboard/add-money"
          className={
            currentPath === "/dashboard/add-money"
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Add Money
        </Link>

        <Link
          href="/dashboard/transactions"
          className={
            currentPath === "/dashboard/transactions"
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Transactions
        </Link>

        <Link
          href="/dashboard/make-payment"
          className={
            currentPath === "/dashboard/make-payment"
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Make Payment
        </Link>

        <Link
          href="/dashboard/create-invoice"
          className={
            currentPath === "/dashboard/create-invoice"
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Create Invoice
        </Link>

        <Link
          href="/dashboard/profile"
          className={
            currentPath === "/dashboard/profile"
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Profile
        </Link>

        <Link
          href="/dashboard/settings"
          className={
            currentPath === "/dashboard/settings"
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Settings
        </Link>

        <Link
          href="/dashboard/change-password"
          className={
            currentPath === "/dashboard/change-password"
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Change Password
        </Link>

        <Link
          href="/dashboard/support-ticket"
          className={
            currentPath === "/dashboard/support-ticket"
              ? "px-4 py-2 rounded-lg bg-blue-600 text-white"
              : "px-4 py-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700"
          }
        >
          Support Ticket
        </Link>
      </nav>
    </aside>
  );
};

export default DashboardSidebar;
