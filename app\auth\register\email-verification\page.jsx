"use client";
import axios from "axios";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "react-toastify";

const EmailVerify = () => {
  const [loading, setLoading] = useState(false);
  const [verifyNumber, setVerifyNumber] = useState("");
  const router = useRouter();

  const verifyEmailNumber = async (e) => {
    e.preventDefault();
    if (verifyNumber.length !== 6) {
      toast.error("The otp field must be 6 digits.");
      return;
    }

    setLoading(true);

    try {
      const requestBody = { otp: verifyNumber };

      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/user/validate-verify-email`,
        requestBody,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );

      toast.success("<PERSON>ail verified successfully!");
      router.push("/dashboard");
    } catch (e) {
      if (e.response?.status === 422) {
        toast.error(e.response.data.message);
      } else {
        console.log("verify email number error:", e.response?.data?.message);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="flex flex-col items-center justify-center h-screen">
        <div className="w-full max-w-md bg-white shadow-lg rounded-lg p-8">
          <h1 className="text-3xl font-bold mb-5">Email Verification</h1>
          <form onSubmit={verifyEmailNumber}>
            <div className="mb-4">
              <label
                className="block text-gray-700 text-sm font-bold mb-2"
                htmlFor="otp"
              >
                Verify Email
              </label>
              <input
                className="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="otp"
                type="text"
                placeholder="Enter 6-digit OTP"
                value={verifyNumber}
                onChange={(e) => setVerifyNumber(e.target.value)}
              />
            </div>
            <div className="flex justify-center">
              <button
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full"
                type="submit"
              >
                {loading ? "Sending..." : "Send"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default EmailVerify;
