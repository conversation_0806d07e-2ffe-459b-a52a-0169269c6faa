"use client";
import CreateInvoice from "@/app/components/createInvoice/CreateInvoice";
import InvoiceHistory from "@/app/components/invoiceHistory/InvoiceHistory";
import { useState } from "react";

const Page = () => {
  const [activeTab, setActiveTab] = useState("create-invoice");

  return (
    <div>
      <div className="inline-flex gap-3 mb-10 bg-slate-900 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab("create-invoice")}
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              activeTab === "create-invoice"
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Create Invoice
        </button>
        <button
          onClick={() => setActiveTab("history")}
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              activeTab === "history"
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Invoice History
        </button>
      </div>

      <div>
        {activeTab === "create-invoice" && <CreateInvoice />}
        {activeTab === "history" && <InvoiceHistory />}
      </div>
    </div>
  );
};

export default Page;
