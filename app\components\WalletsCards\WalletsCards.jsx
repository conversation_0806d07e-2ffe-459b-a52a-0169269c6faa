import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";
import axios from "axios";
import Cookies from "js-cookie";

const WalletsCards = ({ walletsCardsData, onWalletCreated }) => {
  const [showModal, setShowModal] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState("");
  const [currencies, setCurrencies] = useState([]);
  const [loading, setLoading] = useState(false);

  // Card gradient colors
  const colors = [
    "from-purple-500 via-purple-600 to-pink-500",
    "from-blue-500 via-purple-500 to-purple-600",
    "from-blue-400 via-blue-500 to-blue-600",
    "from-purple-500 via-purple-600 to-indigo-600",
  ];

  // Fetch all currencies
  const fetchCurrencies = async () => {
    try {
      const { data } = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/get-currencies`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      setCurrencies(data.data);
    } catch (error) {
      toast.error(
        error.response?.data?.message || "Failed to fetch currencies"
      );
    }
  };

  // Filter only currencies user doesn’t already have
  const getAvailableCurrencies = () => {
    const existingCodes = walletsCardsData.map((w) => w.code);

    return currencies.filter(
      (c) =>  !existingCodes.includes(c.code)
    );
  };

  // Create a wallet
  const createWallet = async () => {
    if (!selectedCurrency) {
      toast.error("Please select a currency");
      return;
    }
    setLoading(true);

    try {
      await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/wallets`,
        { currency_id: selectedCurrency },
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );

      toast.success("Wallet created successfully!");
      closeModal();

      onWalletCreated
        ? onWalletCreated()
        : setTimeout(() => location.reload(), 1000);
    } catch (error) {
      toast.error(error.response?.data?.message || "Failed to create wallet");
    } finally {
      setLoading(false);
    }
  };

  const openModal = () => {
    if (getAvailableCurrencies().length === 0) {
      toast.info("You already have wallets for all available currencies");
      return;
    }
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedCurrency("");
  };

  useEffect(() => {
    fetchCurrencies();
  }, []);

  return (
    <>
      {/* Wallets List */}
      <div className="flex gap-6 w-full overflow-x-auto pb-2">
        {walletsCardsData?.map((wallet, i) => (
          <div
            key={wallet.id}
            className={`bg-gradient-to-br ${colors[i % colors.length]} 
            rounded-2xl min-w-[280px] p-6 text-white shadow-lg`}
          >
            {/* Wallet header */}
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                {wallet.icon && wallet.icon !== "null" ? (
                  <img
                    src={wallet.icon}
                    alt={wallet.name}
                    className="w-6 h-6"
                  />
                ) : (
                  <span className="font-bold">{wallet.symbol}</span>
                )}
              </div>
              <div>
                <h3 className="font-semibold">{wallet.name}</h3>
                <p className="text-sm opacity-80">{wallet.code}</p>
              </div>
            </div>

            {/* Balance */}
            <h2 className="text-2xl font-bold mb-6">
              {wallet.formatted_balance} {wallet.code}
            </h2>

            {/* Actions */}
            <div className="flex gap-3">
              <button className="bg-white bg-opacity-20 px-4 py-2 rounded-full text-sm">
                Top Up
              </button>
              <button className="bg-white bg-opacity-20 px-4 py-2 rounded-full text-sm">
                Withdraw
              </button>
            </div>
          </div>
        ))}

        {/* Add Wallet Animated Button */}
        <div className="border rounded-2xl bg-slate-600 shadow-2xl flex items-center justify-center">
          <button
            onClick={openModal}
            className="text-3xl min-w-[120px] h-full text-white rounded-2xl"
          >
            +
          </button>
        </div>
      </div>

      {/* Create Wallet Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black/50 flex justify-center items-center z-50">
          <div className="bg-white p-6 rounded-xl w-full max-w-md mx-4 shadow-lg animate-fadeIn">
            <h2 className="text-xl font-bold mb-6 text-center">
              Create New Wallet
            </h2>

            {/* Currency Select */}
            <select
              className="w-full py-2 px-3 border rounded mb-6"
              value={selectedCurrency}
              onChange={(e) => setSelectedCurrency(e.target.value)}
              disabled={loading}
            >
              <option value="">Select Currency</option>
              {getAvailableCurrencies().map((c) => (
                <option key={c.id} value={c.id}>
                  {c.name} ({c.code})
                </option>
              ))}
            </select>

            {/* Buttons */}
            <div className="flex justify-center gap-3">
              <button
                onClick={createWallet}
                disabled={loading || !selectedCurrency}
                className="px-6 py-2 bg-blue-600 text-white rounded disabled:bg-gray-400"
              >
                {loading ? "Creating..." : "Create"}
              </button>
              <button
                onClick={closeModal}
                disabled={loading}
                className="px-6 py-2 bg-red-600 text-white rounded disabled:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default WalletsCards;
