import Cookies from "js-cookie";

class TokenService {
  constructor() {
    this._accessToken = null;

    if (typeof window !== 'undefined') {
      this._accessToken = Cookies.get('token') || null;
    }
  }

  static getInstance() {
    if (!TokenService.instance) {
      TokenService.instance = new TokenService();
    }
    return TokenService.instance;
  }

  get accessToken() {
    return this._accessToken;
  }

  async saveAccessToken(token, days = 7) {
    this._accessToken = token;
    if (typeof window !== 'undefined') {
      Cookies.set('token', token, { 
        expires: days,
        secure: true,
        sameSite: 'strict',
        path: '/'
      });
    }
  }

  clearToken() {
    this._accessToken = null;
    if (typeof window !== 'undefined') {
      Cookies.remove('token', { path: '/' });
    }
  }

  isAuthenticated() {
    return !!this._accessToken;
  }
}

export default TokenService;
