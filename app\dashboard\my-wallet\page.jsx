"use client";
import axios from "axios";
import Cookies from "js-cookie";
import Image from "next/image";
import { useEffect, useState } from "react";
import { RiDeleteBin6Line } from "react-icons/ri";
import { toast } from "react-toastify";

const page = () => {
  const [wallets, setWallets] = useState([]);
  const [currencySymbol, setCurrencySymbol] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [walletToDelete, setWalletToDelete] = useState(null);
  const [fetching, setFetching] = useState(false);
  const [currencies, setCurrencies] = useState([]);
  const [selectedCurrencyId, setSelectedCurrencyId] = useState(null);

  // get general data for currency symbol
  const getGeneralData = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/get-settings?key=currency_symbol`
      );
      setCurrencySymbol(res.data.data);
    } catch (e) {
      console.log(e.response.data);
    }
  };

  // get wallets data
  const walletsData = async () => {
    // setFetching(true);
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/wallets`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
        }
      );
      setWallets(res.data.data.wallets);
    } catch (e) {
      console.log(e.response.data);
    } finally {
      // setFetching(false);
    }
  };

  // delete wallet
  const handleDeleteWallet = async () => {
    try {
      await axios.delete(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/wallets/${walletToDelete}`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
        }
      );
      setShowModal(false);
      setWalletToDelete(null);
      walletsData();
      toast.success("Wallet deleted successfully!");
    } catch (e) {
      console.log(e.response?.data);
      if (e.response?.status === 422) {
        toast.error(e.response.data.message);
        setShowModal(false);
      }
    }
  };

  // fetch all currency list
  const fetchCurrencyList = async () => {
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/get-currencies`
      );
      setCurrencies(res.data.data);
      // console.log(res.data.data);
    } catch (e) {
      console.log(e.response.data);
    }
  };

  // add new wallet
  const handleAddWallet = async (selectedCurrencyId) => {
    try {
      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/wallets`,
        {
          currency_id: selectedCurrencyId,
        },
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
        }
      );
      setShowAddModal(false);
      walletsData();
    } catch (e) {
      if (e.response?.status === 422) {
        toast.error(e.response.data.message);
      }
      console.log(e.response.data);
    }
  };

  useEffect(() => {
    getGeneralData();
    walletsData();
    fetchCurrencyList();
  }, []);

  return (
    <div>
      <div className="title flex justify-between items-center mb-5">
        <h1 className="text-2xl font-bold">My Wallet</h1>
        <button
          onClick={() => {
            setShowAddModal(true);
          }}
          className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Add New Wallet
        </button>
      </div>

      {fetching ? (
        <p className="text-center text-xl">Loading wallets...</p>
      ) : (
        <div className="grid grid-cols-4 gap-6">
          {wallets.map((wallet) => (
            <div
              key={wallet.id}
              className="bg-white p-4 rounded-lg shadow relative"
            >
              {!wallet.is_default && (
                <button
                  onClick={() => {
                    setWalletToDelete(wallet.id);
                    setShowModal(true);
                  }}
                  className="absolute top-2 right-2 p-2 bg-red-100 h-8 w-8 rounded-full flex justify-center items-center"
                >
                  <RiDeleteBin6Line className="text-red-500" />
                </button>
              )}

              <div className="flex items-center gap-2 mb-3">
                <div className="img-box">
                  <div className="img">
                    {wallet.is_default ? (
                      <div className="rounded-full w-[50px] h-[50px] bg-gray-100 flex justify-center items-center">
                        <p className="text-3xl font-bold">{currencySymbol}</p>
                      </div>
                    ) : (
                      <Image
                        src={wallet.icon}
                        width={50}
                        height={50}
                        alt={wallet.name}
                        className="rounded-full w-[50px] h-[50px]"
                      />
                    )}
                  </div>
                </div>
                <div className="text-box">
                  <p className="text-2xl font-bold">{wallet.name}</p>
                  <p className="text-sm">{wallet.code}</p>
                </div>
              </div>

              <h5 className="text-2xl font-bold">
                {wallet.formatted_balance} {wallet.code}
              </h5>

              <div className="buttons flex items-center gap-2 mt-5">
                <button className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                  Top Up
                </button>
                <button className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                  Withdraw
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <div className="bg-white p-6 rounded-lg w-80">
            <h2 className="text-xl font-bold mb-4 text-center">
              Confirm Delete
            </h2>
            <p className="mb-6 text-center">
              Are you sure you want to delete this wallet?
            </p>
            <div className="flex justify-center gap-3">
              <button
                onClick={() => setShowModal(false)}
                className="px-4 py-2 rounded bg-gray-300 hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteWallet}
                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700"
              >
                delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Wallet Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <div className="bg-white p-6 rounded-lg w-80">
            <h2 className="text-xl font-bold mb-4 text-center">
              Create New Wallet
            </h2>
            <div className="mt-4">
              <label
                className="block text-gray-700 text-sm font-bold mb-2"
                htmlFor="addWallet"
              >
                Currency
              </label>
              <select
                className="border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="addWallet"
                value={selectedCurrencyId}
                onChange={(e) => setSelectedCurrencyId(e.target.value)}
              >
                <option value="">Select Currency</option>
                {currencies
                  .filter(
                    (currency) =>
                      !wallets.some((wallet) => wallet.code === currency.code)
                  )
                  .map((currency) => (
                    <option key={currency.id} value={currency.id}>
                      {currency.name}
                    </option>
                  ))}
              </select>
            </div>
            <div className="flex justify-center gap-3 mt-4">
              <button
                onClick={() => handleAddWallet(selectedCurrencyId)}
                className="px-4 py-2 rounded bg-blue-600 text-white"
              >
                Create
              </button>
              <button
                onClick={() => setShowAddModal(false)}
                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700"
              >
                cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default page;
