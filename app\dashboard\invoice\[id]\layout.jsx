"use client";
import Link from "next/link";
import { useParams, usePathname } from "next/navigation";

const InvoiceLayout = ({ children }) => {
  const params = useParams();
  const pathname = usePathname();
  const invoiceId = params.id;

  const isActive = (path) => {
    return pathname === path;
  };

  return (
    <div>
      {/* Breadcrumb Navigation */}
      <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
        <Link
          href="/dashboard"
          className="hover:text-blue-600 transition-colors"
        >
          Dashboard
        </Link>
        <span>/</span>
        <Link
          href="/dashboard/create-invoice/history"
          className="hover:text-blue-600 transition-colors"
        >
          Invoice History
        </Link>
        <span>/</span>
        <span className="text-gray-900 font-medium">Invoice {invoiceId}</span>
      </nav>

      {/* Tab Navigation */}
      <div className="inline-flex gap-1 mb-6 bg-gray-100 p-1 rounded-lg">
        <Link
          href={`/dashboard/invoice/${invoiceId}/view`}
          className={`px-4 py-2 rounded-md font-medium text-sm transition-colors duration-200
            ${
              isActive(`/dashboard/invoice/${invoiceId}/view`)
                ? "bg-white text-blue-600 shadow-sm"
                : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
            }`}
        >
          👁️ View
        </Link>
        <Link
          href={`/dashboard/invoice/${invoiceId}/edit`}
          className={`px-4 py-2 rounded-md font-medium text-sm transition-colors duration-200
            ${
              isActive(`/dashboard/invoice/${invoiceId}/edit`)
                ? "bg-white text-blue-600 shadow-sm"
                : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
            }`}
        >
          ✏️ Edit
        </Link>
      </div>

      {/* Page Content */}
      <div>{children}</div>
    </div>
  );
};

export default InvoiceLayout;
