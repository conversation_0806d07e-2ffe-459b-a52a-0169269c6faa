export const normalizeFields = (fieldsArray) => {
  const config = {};
  fieldsArray.forEach(({ key, value }) => {
    const [field, type] = key.split("_"); // e.g. "username_show" -> ["username", "show"]
    if (!config[field]) config[field] = {};
    config[field][type] = value === "1"; // convert to boolean
  });
  return config;
};

export const getSettingValue = (settings, key) => {
  if (!settings?.data) return null;
  const found = settings.data.find((item) => item.name === key);
  return found ? found.value : null;
};

// calculate charge helper function
export const calculateCharge = (gatewayMethod, siteCurrency) => {
  if (!gatewayMethod) return { mainCharge: "", displayChargeType: "" };

  const gatewayCurrency = gatewayMethod?.currency;
  const gatewayCharge = gatewayMethod?.charge;
  const currencyType = gatewayMethod?.currency_type;
  const chargeType = gatewayMethod?.charge_type;

  let mainCharge = "";

  if (
    siteCurrency?.trim().toUpperCase() === gatewayCurrency?.trim().toUpperCase()
  ) {
    mainCharge = Number(gatewayCharge).toFixed(2);
  } else if (currencyType?.toLowerCase() === "crypto") {
    mainCharge = Number(gatewayCharge).toFixed(8);
  } else {
    mainCharge = Number(gatewayCharge).toFixed(2);
  }

  let displayChargeType = chargeType === "percentage" ? "%" : gatewayCurrency;

  return { mainCharge, displayChargeType };
};

// calculate min and max amount helper function
export const calculateMinMaxAmount = (gatewayMethod, siteCurrency) => {
  if (!gatewayMethod) return { minAmount: "", maxAmount: "" };

  const gatewayCurrency = gatewayMethod?.currency;
  const gatewayMinCurrency = gatewayMethod?.minimum_deposit;
  const gatewayMaxCurrency = gatewayMethod?.maximum_deposit;
  const currencyType = gatewayMethod?.currency_type;

  let minAmount = "";
  let maxAmount = "";

  if (
    siteCurrency?.trim().toUpperCase() === gatewayCurrency?.trim().toUpperCase()
  ) {
    minAmount = Number(gatewayMinCurrency).toFixed(2);
    maxAmount = Number(gatewayMaxCurrency).toFixed(2);
  } else if (currencyType?.toLowerCase() === "crypto") {
    minAmount = Number(gatewayMinCurrency).toFixed(8);
    maxAmount = Number(gatewayMaxCurrency).toFixed(8);
  } else {
    minAmount = Number(gatewayMinCurrency).toFixed(2);
    maxAmount = Number(gatewayMaxCurrency).toFixed(2);
  }

  return { minAmount, maxAmount };
};

// calculate min and max amount helper function
export const dynamicDecimals = ({
  currencyCode,
  siteCurrencyCode,
  siteCurrencyDecimals,
  isCrypto,
}) => {
  const dynamicCurrency = currencyCode === siteCurrencyCode;
  // console.log("dynamicCurrency", dynamicCurrency);

  if (dynamicCurrency) {
    // console.log("siteCurrencyDecimals", siteCurrencyDecimals);
    return siteCurrencyDecimals;
  } else {
    // console.log("isCrypto", isCrypto);
    return isCrypto ? 8 : 2;
  }
};
