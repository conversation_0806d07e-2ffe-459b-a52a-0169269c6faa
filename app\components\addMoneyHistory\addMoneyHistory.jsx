"use client";

import axios from "axios";
import Cookies from "js-cookie";
import { useEffect, useState } from "react";

const AddMoneyHistory = () => {
  const [depositHistory, setDepositHistory] = useState([]);
  console.log("depositHistory:", depositHistory);

  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState();
  const [lastPage, setLastPage] = useState();
  const [totalPage, setTotalPage] = useState();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("All");
  const [loading, setLoading] = useState(false);

  const fetchDepositHistory = async () => {
    try {
      setLoading(true);
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/transactions`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
          params: {
            page: currentPage,
            type: "all-deposit",
            ...(statusFilter !== "All" && { status: statusFilter }),
            ...(searchQuery !== "" && { txn: searchQuery }),
          },
        }
      );
      setDepositHistory(res.data.data);
      const perPage = res.data.data.meta.per_page;
      setPerPage(perPage);
      const lastPage = res.data.data.meta.last_page;
      setLastPage(lastPage);
      const totalPage = res.data.data.meta.total;
      setTotalPage(totalPage);
    } catch (e) {
      console.log(e.response.data);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDepositHistory();
  }, [currentPage]);

  const handleSearch = () => {
    setCurrentPage(1);
    fetchDepositHistory();
  };

  return (
    <div className="p-6 bg-white rounded-lg">
      <div>
        {/* Header */}
        <div className="inline-flex p-3 rounded-lg items-center gap-4 mb-6 bg-gray-200">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <input
                type="text"
                placeholder="Search Trx ID"
                className="bg-white border border-gray-300 rounded px-3 py-1 text-sm text-gray-700 focus:outline-none focus:border-blue-600"
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  setStatusFilter("All");
                }}
              />
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Status</span>
              <select
                className="bg-white border border-gray-300 rounded px-3 py-1 text-sm text-gray-700 focus:outline-none focus:border-blue-600"
                value={statusFilter}
                onChange={(e) => {
                  setStatusFilter(e.target.value);
                  setSearchQuery("");
                }}
              >
                <option value="">All</option>
                <option value="success">Success</option>
                <option value="pending">Pending</option>
                <option value="failed">Failed</option>
              </select>
            </div>
          </div>

          <button
            onClick={handleSearch}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              ></path>
            </svg>
            <span className="text-sm">Search</span>
          </button>
        </div>

        {/* Table */}
        <div className="bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-100">
                <tr className="border-b border-gray-300">
                  <th className="text-left py-4 px-6 text-sm font-medium text-gray-700">
                    Description
                  </th>
                  <th className="text-left py-4 px-6 text-sm font-medium text-gray-700">
                    Transaction ID
                  </th>
                  <th className="text-left py-4 px-6 text-sm font-medium text-gray-700">
                    Wallet
                  </th>
                  <th className="text-left py-4 px-6 text-sm font-medium text-gray-700">
                    Amount
                  </th>
                  <th className="text-left py-4 px-6 text-sm font-medium text-gray-700">
                    Charge
                  </th>
                  <th className="text-left py-4 px-6 text-sm font-medium text-gray-700">
                    Status
                  </th>
                  <th className="text-left py-4 px-6 text-sm font-medium text-gray-700">
                    Method
                  </th>
                </tr>
              </thead>

              {loading ? (
                <tbody>
                  <tr>
                    <td colSpan={7} className="py-4 px-6 text-center">
                      Loading...
                    </td>
                  </tr>
                </tbody>
              ) : (
                <tbody className="divide-y divide-gray-200">
                  {depositHistory?.transactions?.map((item, index) => (
                    <tr
                      className="hover:bg-gray-50 transition-colors duration-150"
                      key={index}
                    >
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <div className="w-3 h-3 bg-white rounded-full"></div>
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-800">
                              {item.description}
                            </div>
                            <div className="text-xs text-gray-500">
                              {item.created_at}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-6 text-sm text-gray-600">
                        {item.tnx}
                      </td>
                      <td className="py-4 px-6 text-sm text-gray-600">
                        {item.wallet_type}
                      </td>
                      <td className="py-4 px-6 text-sm text-gray-800 font-medium">
                        {item.amount}
                      </td>
                      <td className="py-4 px-6 text-sm text-gray-600">
                        {item.charge}
                      </td>
                      <td className="py-4 px-6">
                        {item.status === "Success" ? (
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            Success
                          </span>
                        ) : (
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                            Pending
                          </span>
                        )}
                      </td>
                      <td className="py-4 px-6 text-sm text-gray-600">
                        {item.method}
                      </td>
                    </tr>
                  ))}
                </tbody>
              )}
            </table>
          </div>
        </div>

        {/* Pagination */}
        {depositHistory?.transactions?.length > 0 && searchQuery === "" && (
          <div className="flex items-center justify-center mt-6">
            <nav className="flex items-center space-x-1">
              {/* Prev button */}
              <button
                onClick={async () => {
                  setCurrentPage((prev) => Math.max(prev - 1, 1));
                  await fetchDepositHistory();
                }}
                disabled={currentPage === 1}
                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded-lg transition-colors duration-200 disabled:opacity-50"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M15 19l-7-7 7-7"
                  ></path>
                </svg>
              </button>

              {/* Page numbers */}
              {[...Array(lastPage)].map((_, i) => {
                const pageNum = i + 1;
                return (
                  <button
                    key={pageNum}
                    onClick={() => setCurrentPage(pageNum)}
                    className={`w-8 h-8 rounded-lg font-medium text-sm transition-colors duration-200 ${
                      currentPage === pageNum
                        ? "bg-blue-600 text-white"
                        : "text-gray-500 hover:text-gray-700 hover:bg-gray-200"
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}

              {/* Next button */}
              <button
                onClick={async () => {
                  setCurrentPage((next) => Math.min(next + 1, lastPage));
                  await fetchDepositHistory();
                }}
                disabled={currentPage === lastPage}
                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded-lg transition-colors duration-200 disabled:opacity-50"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 5l7 7-7 7"
                  ></path>
                </svg>
              </button>
            </nav>
          </div>
        )}
      </div>
    </div>
  );
};

export default AddMoneyHistory;
