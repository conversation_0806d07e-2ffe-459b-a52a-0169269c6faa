"use client";
import AddMoney from "@/app/components/addMoney/AddMoney";
import AddMoneyHistory from "@/app/components/addMoneyHistory/AddMoneyHistory";
import { useState } from "react";

const Page = () => {
  const [activeTab, setActiveTab] = useState("add");

  return (
    <div>
      <div className="inline-flex gap-3 mb-10 bg-slate-900 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab("add")}
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              activeTab === "add"
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          Add Money
        </button>
        <button
          onClick={() => setActiveTab("history")}
          className={`px-4 py-2 rounded-lg font-semibold text-base transition-colors duration-200
            ${
              activeTab === "history"
                ? "bg-blue-600 text-white"
                : "text-gray-300 hover:bg-slate-700"
            }`}
        >
          History
        </button>
      </div>

      <div>
        {activeTab === "add" && <AddMoney />}
        {activeTab === "history" && <AddMoneyHistory />}
      </div>
    </div>
  );
};

export default Page;
